// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, virtual } from '@feathersjs/schema'
import { Type, getValidator, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'

import type { HookContext } from '../../declarations'
import { dataValidator, queryValidator } from '../../validators'
import { organizerSchema } from '../organizers/organizers.schema'
import type { TournamentsService } from './tournaments.class'

// Main data model schema
export const tournamentSchema = Type.Object(
  {
    id: Type.Number(),
    organizerId: Type.Number(),
    name: Type.String(),
    description: Type.Optional(Type.String()),
    coverImage: Type.Optional(Type.String()),
    icon: Type.Optional(Type.String()),
    isActive: Type.Optional(Type.Boolean()),
    createdAt: Type.Optional(Type.String({ format: 'date-time' })),
    updatedAt: Type.Optional(Type.String({ format: 'date-time' })),
    deletedAt: Type.Optional(Type.String({ format: 'date-time' })),
    createdBy: Type.Optional(Type.Number()),
    updatedBy: Type.Optional(Type.Number()),
    deletedBy: Type.Optional(Type.Number()),
    // Virtual field for related organizer
    organizer: Type.Optional(Type.Ref(organizerSchema))
  },
  { $id: 'Tournament', additionalProperties: false }
)
export type Tournament = Static<typeof tournamentSchema>
export const tournamentValidator = getValidator(tournamentSchema, dataValidator)
export const tournamentResolver = resolve<Tournament, HookContext<TournamentsService>>({
  // Add a virtual field to load the related organizer
  organizer: virtual(async (tournament, context) => {
    if (tournament.organizerId) {
      return context.app.service('organizers').get(tournament.organizerId)
    }
    return undefined
  })
})

export const tournamentExternalResolver = resolve<Tournament, HookContext<TournamentsService>>({})

// Schema for creating new entries
export const tournamentDataSchema = Type.Pick(
  tournamentSchema,
  ['organizerId', 'name', 'description', 'coverImage', 'icon', 'isActive'],
  {
    $id: 'TournamentData'
  }
)
export type TournamentData = Static<typeof tournamentDataSchema>
export const tournamentDataValidator = getValidator(tournamentDataSchema, dataValidator)
export const tournamentDataResolver = resolve<Tournament, HookContext<TournamentsService>>({
  createdAt: async () => {
    return new Date().toISOString()
  },
  updatedAt: async () => {
    return new Date().toISOString()
  }
})

// Schema for updating existing entries
export const tournamentPatchSchema = Type.Partial(tournamentSchema, {
  $id: 'TournamentPatch'
})
export type TournamentPatch = Static<typeof tournamentPatchSchema>
export const tournamentPatchValidator = getValidator(tournamentPatchSchema, dataValidator)
export const tournamentPatchResolver = resolve<Tournament, HookContext<TournamentsService>>({
  updatedAt: async () => {
    return new Date().toISOString()
  }
})

// Schema for allowed query properties
export const tournamentQueryProperties = Type.Pick(tournamentSchema, [
  'id',
  'organizerId',
  'name',
  'isActive'
])
export const tournamentQuerySchema = Type.Intersect(
  [
    querySyntax(tournamentQueryProperties),
    // Add additional query properties here
    Type.Object({}, { additionalProperties: false })
  ],
  { additionalProperties: false }
)
export type TournamentQuery = Static<typeof tournamentQuerySchema>
export const tournamentQueryValidator = getValidator(tournamentQuerySchema, queryValidator)
export const tournamentQueryResolver = resolve<TournamentQuery, HookContext<TournamentsService>>({})
