import type { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
    await knex.schema.alterTable('match_registrations', (table) => {
        table.integer('equipmentId').nullable().references('id').inTable('equipment')
        table.jsonb('scores').nullable().comment('JSON scores data from points_json field')
        
        // Add index for equipment lookup
        table.index(['equipmentId'], 'idx_match_registrations_equipment_id')
    })
}

export async function down(knex: Knex): Promise<void> {
    await knex.schema.alterTable('match_registrations', (table) => {
        table.dropIndex(['equipmentId'], 'idx_match_registrations_equipment_id')
        table.dropColumn('equipmentId')
        table.dropColumn('scores')
    })
}
