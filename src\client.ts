import authenticationClient from '@feathersjs/authentication-client'
import type { AuthenticationClientOptions } from '@feathersjs/authentication-client'
// For more information about this file see https://dove.feathersjs.com/guides/cli/client.html
import { feathers } from '@feathersjs/feathers'
import type { Application, TransportConnection } from '@feathersjs/feathers'

import { clubClient } from './services/clubs/clubs.shared'
import { equipmentClient } from './services/equipment/equipment.shared'
import { matchRegistrationsClient } from './services/match-registrations/match-registrations.shared'
import { matchResultsClient } from './services/match-results/match-results.shared'
import { matchesClient } from './services/matches/matches.shared'
import { messagesClient } from './services/messages/messages.shared'
import { organizersClient } from './services/organizers/organizers.shared'
import { playersClient } from './services/players/players.shared'
import { tournamentsClient } from './services/tournaments/tournaments.shared'
import { userClient } from './services/users/users.shared'

export type { Club, ClubData, ClubQuery, ClubPatch } from './services/clubs/clubs.shared'

export type {
  Organizer,
  OrganizerData,
  OrganizerQuery,
  OrganizerPatch
} from './services/organizers/organizers.shared'

export type {
  MatchResult,
  MatchResultData,
  MatchResultQuery,
  MatchResultPatch
} from './services/match-results/match-results.shared'

export type {
  Equipment,
  EquipmentData,
  EquipmentQuery,
  EquipmentPatch
} from './services/equipment/equipment.shared'

export type { Player, PlayerData, PlayerQuery, PlayerPatch } from './services/players/players.shared'

export type { Message, MessageData, MessageQuery, MessagePatch } from './services/messages/messages.shared'

export type { Match, MatchData, MatchQuery, MatchPatch } from './services/matches/matches.shared'

export type { Tournament, TournamentData, TournamentQuery, TournamentPatch } from './services/tournaments/tournaments.shared'

export type { User, UserData, UserQuery, UserPatch } from './services/users/users.shared'

export type {
  MatchRegistration,
  MatchRegistrationData,
  MatchRegistrationQuery,
  MatchRegistrationPatch
} from './services/match-registrations/match-registrations.shared'

export type {
  AuthenticationData,
  Authentication,
  AuthenticationQuery
} from './authentication'

export interface Configuration {
  connection: TransportConnection<ServiceTypes>
}

export interface ServiceTypes {}

export type ClientApplication = Application<ServiceTypes, Configuration>

/**
 * Returns a typed client for the ap-api-feathers app.
 *
 * @param connection The REST or Socket.io Feathers client connection
 * @param authenticationOptions Additional settings for the authentication client
 * @see https://dove.feathersjs.com/api/client.html
 * @returns The Feathers client application
 */
export const createClient = <Configuration = any,>(
  connection: TransportConnection<ServiceTypes>,
  authenticationOptions: Partial<AuthenticationClientOptions> = {}
) => {
  const client: ClientApplication = feathers()

  client.configure(connection)
  client.configure(authenticationClient(authenticationOptions))
  client.set('connection', connection)

  client.configure(userClient)
  client.configure(matchesClient)
  client.configure(messagesClient)
  client.configure(playersClient)
  client.configure(equipmentClient)
  client.configure(matchResultsClient)
  client.configure(organizersClient)
  client.configure(clubClient)
  client.configure(matchRegistrationsClient)
  client.configure(tournamentsClient)
  return client
}
