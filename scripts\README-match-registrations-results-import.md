# Match Registrations and Results Import Script

This script imports match registrations and results from a single CSV file that contains both registration and results data for each player.

## Prerequisites

Before running the import:

1. **Run the migration** to add the required fields to the match_registrations table:
   ```bash
   pnpm run migrate
   ```

2. **Ensure related data exists**:
   - Matches must be imported first (with legacyId values)
   - Players must be imported first (with legacyId values)  
   - Equipment must be imported first (with legacyId values)

## CSV File Format

The CSV file should contain the following fields:

| Field | Description | Maps to |
|-------|-------------|---------|
| `id` | Legacy registration ID | Not used directly |
| `active` | Whether registration is active | Not used directly |
| `competition_id` | Legacy match ID | Resolved to `matchId` via matches.legacyId |
| `player_id` | Legacy player ID | Resolved to `playerId` via players.legacyId |
| `confirmed` | Whether registration is confirmed | `isConfirmed` |
| `create_time` | Registration creation time | Not used (auto-generated) |
| `payed` | Whether registration is paid | `isPaid` |
| `rank_points` | Ranking points | Not used directly |
| `place` | Final placement | `place` in match-results |
| `equipment_category` | Equipment category | `styleDivision` |
| `points` | Points scored | `points` in match-results |
| `max_points` | Maximum possible points | `maxPoints` in match-results |
| `age_category` | Age category | `ageDivision` |
| `equipment_control` | Equipment control status | Not used directly |
| `deleted` | Whether record is deleted | Not used directly |
| `points_json` | JSON scores data | `scores` (JSONB) |
| `distance` | Shooting distance | Not used directly |
| `rating` | Player rating | Not used directly |
| `rating_selected` | Selected rating | Not used directly |
| `remove_time` | Removal timestamp | Not used directly |
| `bow_id` | Legacy equipment ID | Resolved to `equipmentId` via equipment.legacyId |

## Usage

### Windows
```cmd
scripts\import-match-registrations-results.bat path\to\your\file.csv
```

### Linux/Mac
```bash
./scripts/import-match-registrations-results.sh path/to/your/file.csv
```

### Direct TypeScript execution
```bash
pnpm exec ts-node scripts/import-match-registrations-results.ts path/to/your/file.csv
```

## What the script does

1. **Auto-detects delimiter**: Supports tab, semicolon, or comma-separated values
2. **Resolves foreign keys**:
   - Looks up match by `competition_id` → `matches.legacyId`
   - Looks up player by `player_id` → `players.legacyId`
   - Looks up equipment by `bow_id` → `equipment.legacyId`
3. **Creates match registration** with:
   - Basic registration info (matchId, playerId, divisions)
   - Equipment reference (equipmentId)
   - Payment/confirmation status (isPaid, isConfirmed)
   - Scores data (scores as JSONB)
4. **Creates match result** (if points data exists) with:
   - Points scored and maximum points
   - Final placement

## Output

The script provides:
- Progress logging for each record processed
- Summary statistics (registrations created, results created, errors)
- Error log file (`import-match-registrations-results-errors.txt`) for failed records

## Error Handling

- Records with missing match or player references are skipped
- Missing equipment references are allowed (equipmentId will be null)
- Invalid JSON in `points_json` is logged but doesn't stop the import
- All errors are logged to a separate file for review

## Example

```bash
# Import from a tab-separated file
./scripts/import-match-registrations-results.sh data/registrations_and_results.tsv

# Output:
# Importing match registrations and results from /path/to/data/registrations_and_results.tsv
# Detected delimiter: TAB
# First record structure: ['id', 'active', 'competition_id', 'player_id', ...]
# Created registration #1 for match 123, player 456
# Created result for match 123, player 456: 285/300 points, place 5
# ...
# Import completed:
# - Total records processed: 150
# - Registrations imported: 145
# - Results imported: 140
# - Errors: 5
```
