import { promises as fs } from 'fs'
import path from 'path'

import { parse as csvParse } from 'csv-parse/sync'

import { app } from '../src/app'
import type { EquipmentData } from '../src/services/equipment/equipment.schema'

function parseBool(val: string): boolean {
    return val === '1' || val === 'true'
}

async function importEquipment(csvFile: string) {
    const absPath = path.resolve(csvFile)
    let fileContent = await fs.readFile(absPath, 'utf8')
    // Remove UTF-8 BOM if present
    if (fileContent.charCodeAt(0) === 0xFEFF) {
        fileContent = fileContent.slice(1)
    }
    const records: any[] = csvParse(fileContent, {
        columns: true,
        skip_empty_lines: true,
        delimiter: ';'
    })

    const players = app.service('players')
    const equipment = app.service('equipment')

    for (const row of records) {
        console.log(row)
        try {
            // Parse JSON field from string
            const parseJsonField = (value: string | undefined): string | undefined => {
                if (!value || value === '\\N' || value === '-1') return undefined;
                try {
                    JSON.parse(value); // Validate only
                    return value;
                } catch (error) {
                    console.error(`Error parsing JSON value: ${value}`, error);
                    return undefined;
                }
            };

            // Parse arrays for configuration merge
            const parseArrayField = (value: string | undefined): any[] => {
                if (!value || value === '\\N' || value === '-1') return [];
                try {
                    const parsed = JSON.parse(value);
                    return Array.isArray(parsed) ? parsed : [];
                } catch (error) {
                    console.error(`Error parsing array value: ${value}`, error);
                    return [];
                }
            };

            // Find player by legacy ID
            let playerId: number | undefined = undefined;
            if (row['player_id'] && row['player_id'] !== '\\N') {
                const legacyPlayerId = Number(row['player_id']);
                try {
                    const playerResult = await players.find({
                        query: { legacyId: legacyPlayerId, $limit: 1 }
                    });
                    if (playerResult.data && playerResult.data.length > 0) {
                        playerId = playerResult.data[0].id;
                        console.log(`Found player with legacyId ${legacyPlayerId} -> playerId ${playerId}`);
                    } else {
                        console.warn(`Player with legacyId ${legacyPlayerId} not found, skipping equipment`);
                        continue;
                    }
                } catch (err) {
                    console.error(`Error finding player with legacyId ${legacyPlayerId}:`, err);
                    continue;
                }
            }

            // Map equipmentClass
            let equipmentClass = 'BOW'; // default
            if (row['equipment'] === 'kusza') {
                equipmentClass = 'CROSSBOW';
            }

            // Merge configuration fields
            const accessories = parseArrayField(row['accessories']);
            const arrows = parseArrayField(row['arrows']);
            const shotTechniques = parseArrayField(row['shot_techniques']);

            const configuration = {
                accessories,
                arrows,
                techniques: shotTechniques // rename shot_techniques to techniques
            };

            const data: EquipmentData = {
                legacyId: Number(row['id']),
                name: row['name'] || 'Unnamed Equipment',
                equipmentClass,
                type: row['bow_type'] || undefined,
                configuration: JSON.stringify(configuration),
                federationStyles: parseJsonField(row['equipment_categories']) as any,
                isActive: parseBool(row['active']) ?? true,
                playerId,
                isDefault: parseBool(row['default']) ?? false
            };

            // Remove undefined fields to avoid DB DEFAULT issues
            const cleanData = Object.fromEntries(
                Object.entries(data).filter(([_, v]) => v !== undefined)
            ) as EquipmentData;

            // Debug: log prepared data
            console.log('Prepared equipment data:', {
                legacyId: row['id'],
                name: cleanData.name,
                equipmentClass: cleanData.equipmentClass,
                playerId: cleanData.playerId,
                federationStylesType: typeof cleanData.federationStyles
            });

            const createdEquipment = await equipment.create(cleanData);

            console.log(`Imported equipment: ${cleanData.name} (legacyId: ${row['id']}, playerId: ${playerId})`);
        } catch (err) {
            console.error(
                `Failed to import equipment with id=${row['id']}, name=${row['name']}:`,
                (err as any).message
            );
        }
    }
}

if (process.argv.length < 3) {
    console.error('Usage: pnpm tsx scripts/import-equipment.ts <path-to-csv>')
    process.exit(1)
}

importEquipment(process.argv[2])
    .then(() => {
        console.log('Equipment import complete.')
        process.exit(0)
    })
    .catch((err) => {
        console.error('Import failed:', err)
        process.exit(1)
    })
