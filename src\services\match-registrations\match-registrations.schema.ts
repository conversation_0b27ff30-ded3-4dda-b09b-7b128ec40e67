// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, virtual } from '@feathersjs/schema'
import { Type, getValidator, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'

import type { HookContext } from '../../declarations'
import { dataValidator, queryValidator } from '../../validators'
import { matchSchema } from '../matches/matches.schema'
import { playerSchema } from '../players/players.schema'
import type { MatchRegistrationsService } from './match-registrations.class'

// Main data model schema
export const matchRegistrationSchema = Type.Object(
  {
    id: Type.Number(),
    matchId: Type.Number(),
    playerId: Type.Number(),
    registrationDate: Type.String({ format: 'date-time' }),
    status: Type.String(),
    styleDivision: Type.Optional(Type.String()),
    ageDivision: Type.Optional(Type.String()),
    genderDivision: Type.Optional(Type.String()),
    registrationDetails: Type.Optional(Type.Unknown()),
    equipmentId: Type.Optional(Type.Number()),
    scores: Type.Optional(Type.Unknown()),
    createdAt: Type.String({ format: 'date-time' }),
    updatedAt: Type.String({ format: 'date-time' }),
    deletedAt: Type.Optional(Type.String({ format: 'date-time' })),
    createdBy: Type.Optional(Type.Number()),
    updatedBy: Type.Optional(Type.Number()),
    deletedBy: Type.Optional(Type.Number()),
    // Virtual fields for related data
    match: Type.Optional(Type.Ref(matchSchema)),
    player: Type.Optional(Type.Ref(playerSchema)),
    isPaid: Type.Optional(Type.Boolean()),
    isConfirmed: Type.Optional(Type.Boolean())
  },
  { $id: 'MatchRegistration', additionalProperties: false }
)
export type MatchRegistration = Static<typeof matchRegistrationSchema>
export const matchRegistrationValidator = getValidator(matchRegistrationSchema, dataValidator)
export const matchRegistrationResolver = resolve<MatchRegistration, HookContext<MatchRegistrationsService>>({
  match: virtual(async (registration, context) => {
    // Load the related match
    return context.app.service('matches').get(registration.matchId)
  }),
  player: virtual(async (registration, context) => {
    // Load the related player
    return context.app.service('players').get(registration.playerId)
  })
})

export const matchRegistrationExternalResolver = resolve<MatchRegistration, HookContext<MatchRegistrationsService>>({})

// Schema for creating new entries
export const matchRegistrationDataSchema = Type.Pick(
  matchRegistrationSchema,
  [
    'matchId', 'playerId', 'styleDivision', 'ageDivision', 'genderDivision', 'registrationDetails', 'equipmentId', 'scores', 'isPaid', 'isConfirmed'
  ],
  {
    $id: 'MatchRegistrationData'
  }
)
export type MatchRegistrationData = Static<typeof matchRegistrationDataSchema>
export const matchRegistrationDataValidator = getValidator(matchRegistrationDataSchema, dataValidator)
export const matchRegistrationDataResolver = resolve<MatchRegistration, HookContext<MatchRegistrationsService>>({
  scores: (value) => (value !== undefined && typeof value === 'object' ? JSON.stringify(value) : value),
  registrationDate: async () => {
    return new Date().toISOString()
  },
  status: async () => {
    return 'pending'
  },
  createdAt: async () => {
    return new Date().toISOString()
  },
  updatedAt: async () => {
    return new Date().toISOString()
  }
})

// Schema for updating existing entries
export const matchRegistrationPatchSchema = Type.Partial(
  Type.Pick(matchRegistrationSchema, ['status', 'styleDivision', 'ageDivision', 'genderDivision', 'registrationDetails', 'equipmentId', 'scores']),
  {
    $id: 'MatchRegistrationPatch'
  }
)
export type MatchRegistrationPatch = Static<typeof matchRegistrationPatchSchema>
export const matchRegistrationPatchValidator = getValidator(matchRegistrationPatchSchema, dataValidator)
export const matchRegistrationPatchResolver = resolve<MatchRegistration, HookContext<MatchRegistrationsService>>({
  scores: (value) => (value !== undefined && typeof value === 'object' ? JSON.stringify(value) : value),
  updatedAt: async () => {
    return new Date().toISOString()
  }
})

// Schema for allowed query properties
export const matchRegistrationQueryProperties = Type.Pick(matchRegistrationSchema, [
  'id',
  'matchId',
  'playerId',
  'status',
  'styleDivision',
  'ageDivision',
  'genderDivision',
  'equipmentId',
  'registrationDate'
])
export const matchRegistrationQuerySchema = Type.Intersect(
  [
    querySyntax(matchRegistrationQueryProperties),
    // Add additional query properties here
    Type.Object({}, { additionalProperties: false })
  ],
  { additionalProperties: false }
)
export type MatchRegistrationQuery = Static<typeof matchRegistrationQuerySchema>
export const matchRegistrationQueryValidator = getValidator(matchRegistrationQuerySchema, queryValidator)
export const matchRegistrationQueryResolver = resolve<MatchRegistrationQuery, HookContext<MatchRegistrationsService>>({})
