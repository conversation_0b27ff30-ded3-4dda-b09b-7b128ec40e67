// For more information about this file see https://dove.feathersjs.com/guides/cli/service.test.html
import assert from 'assert'

import { app } from '../../../src/app'
import type { PlayerData } from '../../../src/services/players/players.class'

describe('players service', () => {
  const service = app.service('players')
  const usersService = app.service('users')
  const authService = app.service('authentication')

  let userId: number
  let playerId: number
  let accessToken: string
  let userParams: any

  // Create a test user before running player tests
  before(async () => {
    const testUser = await usersService.create({
      email: `test-players-${Date.now()}@example.com`,
      password: 'supersecret'
    })

    userId = testUser.id

    // Authenticate the user
    const authResult = await authService.create({
      strategy: 'local',
      email: testUser.email,
      password: 'supersecret'
    })

    accessToken = authResult.accessToken

    // Create params with authentication
    userParams = {
      provider: 'rest',
      authentication: {
        strategy: 'jwt',
        accessToken
      },
      user: testUser
    }
  })

  // Clean up test user after tests
  after(async () => {
    try {
      await usersService.remove(userId)
    } catch (error) {
      console.error('Error cleaning up test user:', error)
    }
  })

  it('registered the service', () => {
    assert.ok(service, 'Registered the service')
  })

  it('requires authentication', async () => {
    try {
      await service.find()
      assert.fail('Should not allow unauthenticated access')
    } catch (error: any) {
      assert.ok(error, 'Returns an error for unauthenticated access')
    }
  })

  it('creates a player profile with user tracking fields', async () => {
    const playerData: PlayerData = {
      userId: userId,
      firstname: 'Test',
      lastname: 'Player',
      isActive: true,
      sex: 'male',
      country: 'Test Country',
      city: 'Test City',
      equipmentCategory: JSON.stringify(['recurve'])
    }

    const player = await service.create(playerData, userParams)

    assert.ok(player, 'Created a player profile')
    assert.ok(player.id, 'Player has an id')
    assert.equal(player.userId, userId, 'Associates with the correct user')
    assert.equal(player.firstname, playerData.firstname, 'Sets the firstname')
    assert.equal(player.lastname, playerData.lastname, 'Sets the lastname')
    assert.ok(player.equipmentCategory, 'Sets the equipment category')

    // Verify user tracking fields
    assert.equal(player.createdBy, userId, 'Sets createdBy to current user ID')
    assert.equal(player.updatedBy, userId, 'Sets updatedBy to current user ID')
    assert.ok(player.createdAt, 'Sets createdAt timestamp')
    assert.ok(player.updatedAt, 'Sets updatedAt timestamp')

    // Save player ID for later tests
    playerId = player.id
  })

  it('gets a player profile', async () => {
    const player = await service.get(playerId, userParams)

    assert.ok(player, 'Got the player')
    assert.equal(player.id, playerId, 'Got the correct player')
    assert.equal(player.userId, userId, 'User association is maintained')
  })

  it('finds players with pagination', async () => {
    const result = await service.find({
      ...userParams,
      query: {
        $limit: 10
      }
    })

    assert.ok(result.data, 'Returns data array')
    assert.ok(result.total >= 1, 'Returns at least one player')
    assert.ok(result.limit === 10, 'Returns specified limit')
  })

  it('updates a player profile and tracks the user who made the update', async () => {
    const updatedData = {
      firstname: 'Updated',
      lastname: 'Player',
      equipmentCategory: JSON.stringify(['compound'])
    }

    const updated = await service.patch(playerId, updatedData, userParams)

    assert.equal(updated.firstname, updatedData.firstname, 'Updated the firstname')
    assert.equal(updated.lastname, updatedData.lastname, 'Updated the lastname')
    assert.ok(updated.equipmentCategory, 'Updated the equipment category')
    assert.equal(updated.id, playerId, 'ID remained the same')
    assert.equal(updated.userId, userId, 'User association remained the same')

    // Verify user tracking fields for update
    assert.equal(updated.updatedBy, userId, 'Sets updatedBy to current user ID')
    assert.ok(updated.updatedAt, 'Updates the updatedAt timestamp')
  })

  it('removes a player profile and tracks who deleted it', async () => {
    const removed = await service.remove(playerId, userParams)

    assert.ok(removed, 'Removed the player')
    assert.equal(removed.id, playerId, 'Removed the correct player')

    // Verify user tracking fields for deletion
    assert.equal(removed.deletedBy, userId, 'Sets deletedBy to current user ID')
    assert.ok(removed.deletedAt, 'Sets deletedAt timestamp')

    try {
      await service.get(playerId, userParams)
      assert.fail('Should have thrown an error for deleted player')
    } catch (error) {
      assert.ok(error, 'Error thrown when getting deleted player')
    }
  })
})
