// // For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, virtual } from '@feathersjs/schema'
import { Type, getValidator, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'

import type { HookContext } from '../../declarations'
import { dataValidator, queryValidator } from '../../validators'
import { organizerSchema } from '../organizers/organizers.schema'
import { tournamentSchema } from '../tournaments/tournaments.schema' // Corrected import
import type { MatchesService } from './matches.class'

// Main data model schema
export const matchSchema = Type.Object(
  {
    id: Type.Number(),
    legacyId: Type.Optional(Type.Number()),
    name: Type.String(),
    isActive: Type.Boolean(),
    country: Type.Optional(Type.String()),
    city: Type.Optional(Type.String()),
    postcode: Type.Optional(Type.String()),
    address: Type.Optional(Type.String()),
    phone: Type.Optional(Type.String()),
    email: Type.Optional(Type.String()),
    startDate: Type.Optional(Type.String({ format: 'date' })),
    endDate: Type.Optional(Type.String({ format: 'date' })),
    registrationEnds: Type.Optional(Type.String({ format: 'date-time' })),
    description: Type.Optional(Type.String()),
    photo: Type.Optional(Type.String()),
    matchType: Type.Optional(Type.String()),
    equipmentCategories: Type.Optional(Type.Unknown()),
    ageCategories: Type.Optional(Type.Unknown()),
    forWomen: Type.Optional(Type.Boolean()),
    forMen: Type.Optional(Type.Boolean()),
    federation: Type.Optional(Type.String()),
    licenseRequired: Type.Optional(Type.Boolean()),
    organizerId: Type.Optional(Type.Number()),
    maxPlayersAmount: Type.Optional(Type.Number()),
    payments: Type.Optional(Type.Unknown()),
    competitionLevel: Type.Optional(Type.String()),
    international: Type.Optional(Type.Boolean()),
    withoutLimits: Type.Optional(Type.Boolean()),
    publishAt: Type.Optional(Type.String({ format: 'date-time' })),
    completed: Type.Optional(Type.Boolean()),
    latitude: Type.Optional(Type.Number()),
    longitude: Type.Optional(Type.Number()),
    agenda: Type.Optional(Type.Unknown()),
    currency: Type.Optional(Type.String()),
    judges: Type.Optional(Type.Unknown()),
    registrationFinished: Type.Optional(Type.Boolean()),
    attachments: Type.Optional(Type.Unknown()),
    createdAt: Type.Optional(Type.String({ format: 'date-time' })),
    updatedAt: Type.Optional(Type.String({ format: 'date-time' })),
    deletedAt: Type.Optional(Type.String({ format: 'date-time' })),
    createdBy: Type.Optional(Type.Number()),
    updatedBy: Type.Optional(Type.Number()),
    deletedBy: Type.Optional(Type.Number()),
    tournamentId: Type.Optional(Type.Number()),
    tournamentConfirmed: Type.Optional(Type.Boolean()),
    yearly: Type.Optional(Type.Boolean()),
    // Virtual fields for related entities
    organizer: Type.Optional(Type.Ref(organizerSchema)),
    tournament: Type.Optional(Type.Ref(tournamentSchema)) // Corrected schema reference
  },
  { $id: 'Match', additionalProperties: false }
)
export type Match = Static<typeof matchSchema>
export const matchValidator = getValidator(matchSchema, dataValidator)
export const matchResolver = resolve<Match, HookContext<MatchesService>>({
  // Add a virtual field to load the related organizer
  organizer: virtual(async (match: Match, context: HookContext<MatchesService>) => {
    if (match.organizerId) {
      return context.app.service('organizers').get(match.organizerId)
    }
    return undefined
  }),
  // Add a virtual field to load the related tournament
  tournament: virtual(async (match: Match, context: HookContext<MatchesService>) => {
    if (match.tournamentId) {
      return context.app.service('tournaments').get(match.tournamentId)
    }
    return undefined
  })
})

export const matchExternalResolver = resolve<Match, HookContext<MatchesService>>({})

// Schema for creating new entries
export const matchDataSchema = Type.Pick(
  matchSchema,
  [
    'legacyId',
    'name',
    'isActive',
    'country',
    'city',
    'postcode',
    'address',
    'phone',
    'email',
    'startDate',
    'endDate',
    'description',
    'equipmentCategories',
    'ageCategories',
    'forWomen',
    'forMen',
    'maxPlayersAmount',
    'organizerId',
    'tournamentId',
    'tournamentConfirmed'
  ],
  {
    $id: 'MatchData'
  }
)
export type MatchData = Static<typeof matchDataSchema>
export const matchDataValidator = getValidator(matchDataSchema, dataValidator)
export const matchDataResolver = resolve<Match, HookContext<MatchesService>>({
  createdAt: async () => {
    return new Date().toISOString()
  },
  updatedAt: async () => {
    return new Date().toISOString()
  }
})

// Schema for updating existing entries
export const matchPatchSchema = Type.Partial(matchSchema, {
  $id: 'MatchPatch'
})
export type MatchPatch = Static<typeof matchPatchSchema>
export const matchPatchValidator = getValidator(matchPatchSchema, dataValidator)
export const matchPatchResolver = resolve<Match, HookContext<MatchesService>>({
  updatedAt: async () => {
    return new Date().toISOString()
  }
})

// Schema for allowed query properties
export const matchQueryProperties = Type.Pick(matchSchema, ['id', 'name', 'organizerId', 'tournamentId', 'isActive'])
export const matchQuerySchema = Type.Intersect(
  [
    querySyntax(matchQueryProperties),
    // Add additional query properties here
    Type.Object({}, { additionalProperties: false })
  ],
  { additionalProperties: false }
)
export type MatchQuery = Static<typeof matchQuerySchema>
export const matchQueryValidator = getValidator(matchQuerySchema, queryValidator)
export const matchQueryResolver = resolve<MatchQuery, HookContext<MatchesService>>({})
