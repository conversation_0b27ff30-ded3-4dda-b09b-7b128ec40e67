// // For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, virtual } from '@feathersjs/schema'
import { Type, getValidator, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'

import type { HookContext } from '../../declarations'
import { dataValidator, queryValidator } from '../../validators'
import { userSchema } from '../users/users.schema'
import type { PlayersService } from './players.class'

// Main data model schema
export const playerSchema = Type.Object(
  {
    id: Type.Number(),
    legacyId: Type.Optional(Type.Number()),
    userId: Type.Number(),
    isActive: Type.Optional(Type.Boolean()),
    address: Type.Optional(Type.String()),
    zipcode: Type.Optional(Type.String()),
    city: Type.Optional(Type.String()),
    country: Type.Optional(Type.String()),
    phone: Type.Optional(Type.String()),
    birthdate: Type.Optional(Type.String({ format: 'date' })),
    sex: Type.Optional(Type.String()),
    firstname: Type.Optional(Type.String()),
    lastname: Type.Optional(Type.String()),
    latitude: Type.Optional(Type.Number()),
    longitude: Type.Optional(Type.Number()),
    equipmentCategory: Type.Optional(Type.String()),
    avatar: Type.Optional(Type.String()),
    licenses: Type.Optional(Type.Array(Type.Any())),
    activatedAt: Type.Optional(Type.String({ format: 'date-time' })),
    createdAt: Type.Optional(Type.String({ format: 'date-time' })),
    updatedAt: Type.Optional(Type.String({ format: 'date-time' })),
    deletedAt: Type.Optional(Type.String({ format: 'date-time' })),
    createdBy: Type.Optional(Type.Number()),
    updatedBy: Type.Optional(Type.Number()),
    deletedBy: Type.Optional(Type.Number()),
    user: Type.Optional(Type.Ref(userSchema))
  },
  { $id: 'Player', additionalProperties: false }
)
export type Player = Static<typeof playerSchema>
export const playerValidator = getValidator(playerSchema, dataValidator)
export const playerResolver = resolve<Player, HookContext<PlayersService>>({
/*   user: virtual(async (player, context) => {
    // Associate the player with the user
    return context.app.service('users').get(player.userId)
  }) */
})

export const playerExternalResolver = resolve<Player, HookContext<PlayersService>>({})

// Schema for creating new entries
export const playerDataSchema = Type.Pick(
  playerSchema,
  [
    'legacyId',
    'userId',
    'isActive',
    'address',
    'zipcode',
    'city',
    'country',
    'phone',
    'birthdate',
    'sex',
    'firstname',
    'lastname',
    'latitude',
    'longitude',
    'equipmentCategory',
    'avatar',
    'licenses',
    'activatedAt'
  ],
  {
    $id: 'PlayerData'
  }
)
export type PlayerData = Static<typeof playerDataSchema>
export const playerDataValidator = getValidator(playerDataSchema, dataValidator)
export const playerDataResolver = resolve<Player, HookContext<PlayersService>>({
  createdAt: async () => {
    return new Date().toISOString()
  },
  updatedAt: async () => {
    return new Date().toISOString()
  }
})

// Schema for updating existing entries
export const playerPatchSchema = Type.Partial(playerSchema, {
  $id: 'PlayerPatch'
})
export type PlayerPatch = Static<typeof playerPatchSchema>
export const playerPatchValidator = getValidator(playerPatchSchema, dataValidator)
export const playerPatchResolver = resolve<Player, HookContext<PlayersService>>({
  updatedAt: async () => {
    return new Date().toISOString()
  }
})

// Schema for allowed query properties
export const playerQueryProperties = Type.Pick(
  playerSchema,
  [
    'id',
    'legacyId',
    'userId',
    'isActive',
    'address',
    'zipcode',
    'city',
    'country',
    'phone',
    'sex',
    'firstname',
    'lastname',
    'equipmentCategory'
  ]
)
export const playerQuerySchema = Type.Intersect(
  [
    querySyntax(playerQueryProperties),
    // Add additional query properties here
    Type.Object({}, { additionalProperties: false })
  ],
  { additionalProperties: false }
)
export type PlayerQuery = Static<typeof playerQuerySchema>
export const playerQueryValidator = getValidator(playerQuerySchema, queryValidator)
export const playerQueryResolver = resolve<PlayerQuery, HookContext<PlayersService>>({})
