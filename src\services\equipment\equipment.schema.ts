// // For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'

import type { HookContext } from '../../declarations'
import { dataValidator, queryValidator } from '../../validators'
import type { EquipmentService } from './equipment.class'

// Main data model schema
export const equipmentSchema = Type.Object(
  {
    id: Type.Number(),
    legacyId: Type.Optional(Type.Number()),
    name: Type.String(),
    category: Type.Optional(Type.String()),
    equipmentClass: Type.Optional(Type.String()),
    type: Type.Optional(Type.String()),
    configuration: Type.Optional(Type.Unknown()),
    federationStyles: Type.Optional(Type.Unknown()),
    isActive: Type.Boolean({ default: true }),
    playerId: Type.Optional(Type.Number()),
    isDefault: Type.Boolean({ default: false }),
    createdAt: Type.Optional(Type.String({ format: 'date-time' })),
    updatedAt: Type.Optional(Type.String({ format: 'date-time' })),
    deletedAt: Type.Optional(Type.String({ format: 'date-time' })),
    createdBy: Type.Optional(Type.Number()),
    updatedBy: Type.Optional(Type.Number()),
    deletedBy: Type.Optional(Type.Number())
  },
  { $id: 'Equipment', additionalProperties: false }
)
export type Equipment = Static<typeof equipmentSchema>
export const equipmentValidator = getValidator(equipmentSchema, dataValidator)
export const equipmentResolver = resolve<Equipment, HookContext<EquipmentService>>({})

export const equipmentExternalResolver = resolve<Equipment, HookContext<EquipmentService>>({})

// Schema for creating new entries
export const equipmentDataSchema = Type.Pick(
  equipmentSchema,
  ['legacyId', 'name', 'category', 'equipmentClass', 'type', 'configuration', 'federationStyles', 'isActive', 'playerId', 'isDefault'],
  {
    $id: 'EquipmentData'
  }
)
export type EquipmentData = Static<typeof equipmentDataSchema>
export const equipmentDataValidator = getValidator(equipmentDataSchema, dataValidator)
export const equipmentDataResolver = resolve<Equipment, HookContext<EquipmentService>>({
  configuration: (value) => (value !== undefined && typeof value === 'object' ? JSON.stringify(value) : value),
  federationStyles: (value) => (value !== undefined && typeof value === 'object' ? JSON.stringify(value) : value),
  createdAt: async () => {
    return new Date().toISOString()
  },
  updatedAt: async () => {
    return new Date().toISOString()
  }
})

// Schema for updating existing entries
export const equipmentPatchSchema = Type.Partial(equipmentSchema, {
  $id: 'EquipmentPatch'
})
export type EquipmentPatch = Static<typeof equipmentPatchSchema>
export const equipmentPatchValidator = getValidator(equipmentPatchSchema, dataValidator)
export const equipmentPatchResolver = resolve<Equipment, HookContext<EquipmentService>>({
  configuration: (value) => (value !== undefined && typeof value === 'object' ? JSON.stringify(value) : value),
  federationStyles: (value) => (value !== undefined && typeof value === 'object' ? JSON.stringify(value) : value),
  updatedAt: async () => {
    return new Date().toISOString()
  }
})

// Schema for allowed query properties
export const equipmentQueryProperties = Type.Pick(
  equipmentSchema,
  ['id', 'name', 'category', 'equipmentClass', 'type', 'isActive', 'playerId', 'isDefault']
)
export const equipmentQuerySchema = Type.Intersect(
  [
    querySyntax(equipmentQueryProperties),
    // Add additional query properties here
    Type.Object({}, { additionalProperties: false })
  ],
  { additionalProperties: false }
)
export type EquipmentQuery = Static<typeof equipmentQuerySchema>
export const equipmentQueryValidator = getValidator(equipmentQuerySchema, queryValidator)
export const equipmentQueryResolver = resolve<EquipmentQuery, HookContext<EquipmentService>>({})
